"""
CRUD operations for chat history management.
"""
from sqlalchemy.orm import Session
from fastapi import HTTPException
from typing import Optional, List, Dict, Any
import logging
import os
from pathlib import Path

from ..models.sessions import ChatHistory, Session as SessionModel
from ..schemas.sessions import ChatHistoryResponse, ExportResponse, TessellationResponse

logger = logging.getLogger(__name__)


def get_chats_by_session_id(
    db: Session, 
    session_id: str, 
    page: int = 1, 
    limit: int = 10
) -> List[ChatHistoryResponse]:
    """
    Get chat history by session ID (paginated).
    """
    offset = (page - 1) * limit
    chat_entries = (
        db.query(ChatHistory)
        .filter(ChatHistory.session_id == session_id)
        .order_by(ChatHistory.id.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    return [
        ChatHistoryResponse(
            id=entry.id,
            message=entry.message,
            created_at=entry.created_at,
            image_path=entry.image_path,
            part_file_name=entry.part_file_name,
            export_format=entry.export_format,
            material_choice=entry.material_choice
        )
        for entry in chat_entries
    ]


def get_exports_by_session_id(
    db: Session, 
    session_id: str, 
    export_format: Optional[str] = None
) -> List[ExportResponse]:
    """
    Get export file links for a session.
    """
    query = db.query(ChatHistory).filter(ChatHistory.session_id == session_id)
    
    if export_format:
        query = query.filter(ChatHistory.export_format == export_format)
    
    exports = query.all()
    
    if not exports:
        raise HTTPException(status_code=404, detail="No exports found for this session")
    
    # Get BASE_URL for creating download links
    base_url = _get_base_url()
    downloadable_urls = []
    
    for export in exports:
        if export.obj_export:
            url = _create_download_url(export.obj_export, base_url)
            downloadable_urls.append(ExportResponse(format="obj", url=url))
        
        if export.step_export:
            url = _create_download_url(export.step_export, base_url)
            downloadable_urls.append(ExportResponse(format="step", url=url))
    
    return downloadable_urls


def get_tessellation_by_session_id(db: Session, session_id: str) -> TessellationResponse:
    """
    Get tessellation data for a session.
    """
    # Placeholder implementation - replace with actual logic
    return TessellationResponse(
        vertices=[[0, 0, 0], [1, 0, 0], [0, 1, 0]],
        faces=[[0, 1, 2]],
        normals=[[0, 0, 1]]
    )


def add_chat_history_entry(
    db: Session,
    session_id: str,
    message: str,
    output: Optional[str] = None,
    image_path: Optional[str] = None,
    part_file_name: Optional[str] = None,
    export_format: Optional[str] = None,
    material_choice: Optional[str] = None,
    selected_feature_uuid: Optional[str] = None,
    obj_export: Optional[str] = None,
    step_export: Optional[str] = None
) -> ChatHistory:
    """
    Add a new chat history entry.
    """
    try:
        # Ensure session exists
        session = db.query(SessionModel).filter(
            SessionModel.session_id == session_id
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        new_entry = ChatHistory(
            session_id=session_id,
            message=message,
            output=output,
            image_path=image_path,
            part_file_name=part_file_name,
            export_format=export_format,
            material_choice=material_choice,
            selected_feature_uuid=selected_feature_uuid,
            obj_export=obj_export,
            step_export=step_export
        )
        
        db.add(new_entry)
        db.commit()
        db.refresh(new_entry)
        return new_entry
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error adding chat history entry: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to add chat history entry")


def _get_base_url() -> str:
    """
    Get the base URL for creating download links.
    """
    try:
        from ..api.main import BASE_URL
        return BASE_URL
    except ImportError:
        port = int(os.getenv("UVICORN_PORT", 8080))
        domain = os.getenv("DOMAIN", "http://localhost")
        
        if domain == "http://localhost" or domain == "localhost":
            return f"{domain}:{port}"
        else:
            return domain


def _create_download_url(file_path: str, base_url: str) -> str:
    """
    Create a download URL from a file path.
    """
    if file_path.startswith(('http://', 'https://')):
        return file_path
    
    project_root = Path.cwd()
    project_root_str = str(project_root).replace('\\', '/')
    file_path_str = str(file_path).replace('\\', '/')
    
    if project_root_str in file_path_str:
        relative_path = file_path_str.split(project_root_str, 1)[1].lstrip('\\/')
        return f"{base_url}/download/{relative_path}"
    else:
        return f"{base_url}/download/{file_path}" 