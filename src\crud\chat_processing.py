"""
CRUD operations for chat processing and request handling.
"""
from sqlalchemy.orm import Session
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Optional
import logging
import uuid
import random
import re

from ..models.sessions import Session as SessionModel, ChatHistory
from ..schemas.sessions import ChatRe<PERSON>, ChatResponse
from .sessions import create_session, get_session_by_id
from .chat_history import add_chat_history_entry

logger = logging.getLogger(__name__)


def handle_chat_request(
    db: Session, 
    chat_req: ChatRequest, 
    agent, 
    request_origin: str = 'api'
) -> ChatResponse:
    """
    Handle chat request with session management.
    """
    session_id = _resolve_session_id(db, chat_req)
    
    # Ensure session exists
    session = get_session_by_id(db, session_id)
    if not session:
        session_name = chat_req.message[:50].strip() if chat_req.message else "User Session"
        session = create_session(db, session_id, session_name)
    
    # Update the chat request with the resolved session ID
    chat_req.session_id = session_id
    
    # Process the request using the TextToCAD agent
    agent_result = agent.process_request(
        user_text=chat_req.message,
        is_edit_request=False,
        request_origin=request_origin,
        session_id=session_id
    )
    
    # Process the agent result and create response
    response = _process_agent_result(db, chat_req, agent_result, session_id)
    
    return response


def _resolve_session_id(db: Session, chat_req: ChatRequest) -> str:
    """
    Resolve session ID - create new if not provided.
    """
    session_id = chat_req.session_id
    
    # If session_id is provided and not empty, validate it exists
    if session_id and session_id != "":
        existing_session = db.query(SessionModel).filter(
            SessionModel.session_id == session_id
        ).first()
        if existing_session:
            return session_id
        else:
            logger.warning(f"Provided session_id {session_id} does not exist, creating new session")
    
    # Check if message explicitly mentions an existing session ID
    if chat_req.message:
        session_pattern = re.compile(r'session_[a-f0-9]{6}_\d{6}')
        session_matches = session_pattern.findall(chat_req.message)
        if session_matches:
            potential_session_id = session_matches[0]
            existing_session = db.query(SessionModel).filter(
                SessionModel.session_id == potential_session_id
            ).first()
            if existing_session:
                logger.info(f"Found session ID {potential_session_id} mentioned in message")
                return potential_session_id
    
    # Generate completely new session ID
    new_session_id = _generate_session_id()
    logger.info(f"Created new session ID: {new_session_id}")
    return new_session_id


def _generate_session_id() -> str:
    """
    Generate a new unique session ID.
    """
    rand_digits = random.randint(100000, 999999)
    rand_uuid = uuid.uuid4().hex[:6]
    return f"session_{rand_uuid}_{rand_digits}"


def _process_agent_result(
    db: Session, 
    chat_req: ChatRequest, 
    agent_result: dict, 
    session_id: str
) -> ChatResponse:
    """
    Process agent result and create chat response.
    """
    # Determine response content
    if agent_result.get("message") and not agent_result.get("code"):
        chat_response_content = agent_result.get("message")
    elif agent_result.get("error"):
        chat_response_content = f"Error: {agent_result.get('error')}"
    elif agent_result.get("code"):
        chat_response_content = "FreeCAD code generation successful."
    else:
        chat_response_content = "Processing completed."
    
    # Handle export paths
    obj_export_path, step_export_path = _handle_export_paths(chat_req, agent_result)
    
    # Add to chat history
    _add_to_chat_history(db, session_id, chat_req, agent_result, obj_export_path, step_export_path)
    
    # Create download URLs
    obj_url = _create_download_url(obj_export_path) if obj_export_path else None
    step_url = _create_download_url(step_export_path) if step_export_path else None
    
    return ChatResponse(
        chat_response=chat_response_content,
        session_id=session_id,
        obj_export=obj_url,
        step_export=step_url,
        tessellated_export=None,
        attribute_and_transientid_map=None,
        manufacturing_errors=[]
    )


def _handle_export_paths(chat_req: ChatRequest, agent_result: dict) -> tuple:
    """
    Handle export file paths based on export format.
    """
    export_format = chat_req.export_format
    obj_path = agent_result.get("obj_path")
    step_path = agent_result.get("step_path")
    
    if export_format is None or export_format == "":
        return obj_path, step_path
    elif export_format.lower() == "obj":
        return obj_path, None
    elif export_format.lower() == "step":
        return None, step_path
    else:
        return obj_path, step_path


def _add_to_chat_history(
    db: Session,
    session_id: str,
    chat_req: ChatRequest,
    agent_result: dict,
    obj_export_path: Optional[str],
    step_export_path: Optional[str]
):
    """
    Add entry to chat history.
    """
    # Determine output content
    if agent_result.get("error"):
        output = f"ERROR_RESPONSE: {agent_result.get('error')}"
    elif agent_result.get("code"):
        output = f"CODE_GENERATED: {agent_result.get('code')}"
    elif agent_result.get("message"):
        output = agent_result.get("message")
    else:
        output = "Agent produced an unknown response structure."
    
    export_format = chat_req.export_format or "both"
    
    add_chat_history_entry(
        db=db,
        session_id=session_id,
        message=chat_req.message,
        output=output,
        image_path=chat_req.image_path,
        part_file_name=chat_req.part_file_name,
        export_format=export_format,
        material_choice=chat_req.material_choice,
        selected_feature_uuid=chat_req.selected_feature_uuid,
        obj_export=obj_export_path,
        step_export=step_export_path
    )


def _create_download_url(file_path: str) -> str:
    """
    Create download URL from file path.
    """
    from .chat_history import _get_base_url, _create_download_url as create_url
    base_url = _get_base_url()
    return create_url(file_path, base_url) 