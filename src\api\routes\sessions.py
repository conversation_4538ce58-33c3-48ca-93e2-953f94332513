"""
API routes for session management operations.
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, Path
from sqlalchemy.orm import Session
from typing import List
import logging

from ...database.database import get_db
from ...schemas.sessions import SessionInfo, SessionCreate, SessionResponse
from ... import crud

logger = logging.getLogger("tolery-api")

router = APIRouter(
    prefix="/sessions",
    tags=["sessions"],
    responses={404: {"description": "Session not found"}},
)


@router.get(
    "/", 
    summary="List Sessions", 
    response_model=List[SessionInfo]
)
def list_sessions(db: Session = Depends(get_db)):
    """
    List all available sessions with their details.
    """
    try:
        sessions = crud.sessions.get_all_sessions(db)
        return sessions
    except Exception as e:
        logger.error(f"Error listing sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve sessions")


@router.post(
    "/",
    summary="Create Session",
    response_model=SessionInfo
)
def create_session(
    session_data: SessionCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new session. If no session_id is provided, a new one will be generated automatically.
    """
    try:
        session = crud.sessions.create_session(
            db, 
            session_id=session_data.session_id,
            name=session_data.name
        )
        logger.info(f"Created new session: {session.session_id}")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create session")


@router.get(
    "/{session_id}",
    summary="Get Session Info",
    response_model=SessionInfo
)
def get_session(session_id: str, db: Session = Depends(get_db)):
    """
    Get information about a specific session.
    """
    try:
        session = crud.sessions.get_session_by_id(db, session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve session")


@router.delete(
    "/{session_id}",
    summary="Delete Session",
    response_model=SessionResponse
)
def delete_session(
    session_id: str = Path(...),
    db: Session = Depends(get_db)
):
    """
    Delete a session permanently.
    """
    try:
        result = crud.sessions.delete_session(db, session_id)
        return SessionResponse(message=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete session")
