document.addEventListener("DOMContentLoaded", function () {
  const chatToggle = document.getElementById("chat-toggle");
  const chatbotWidget = document.getElementById("chatbot-widget");
  const closeChat = document.getElementById("close-chat");
  const minimizeChat = document.getElementById("minimize-chat");
  const chatForm = document.getElementById("chat-form");
  const userInput = document.getElementById("user-input");
  const codeOutput = document.getElementById("code-output"); // New element for code output
  const sendBtn = document.getElementById("send-btn");
  const refreshBtn = document.getElementById("refresh-btn"); // Added refresh button
  const editModeToggle = document.getElementById("edit-mode-toggle");
  const editModeIndicator = document.getElementById("edit-mode-indicator");
  const chatHistory = document.getElementById("chat-history"); // Chat history element
  const clearHistoryBtn = document.getElementById("clear-history-btn"); // Clear history button
  const attachFileBtn = document.getElementById("attach-file-btn"); // Unified attach file button
  const selectedFileInfo = document.getElementById("selected-file-info"); // Updated to handle both PDF and image files
  const viewStepBtn = document.getElementById("view-step-btn"); // STEP viewer button
  const sessionIndicator = document.getElementById("session-indicator"); // Session indicator
  const sessionIdDisplay = document.getElementById("session-id-display"); // Session ID display

  // Variable to store the latest generated code and selected files
  let latestCode = null;
  let isEditMode = false;
  let selectedPdfFile = null; // Store the selected PDF file
  let selectedImageFile = null; // Store the selected image file
  let currentSessionId = null; // Store the current session ID for conversation continuity

  // Direct style override for parameter requests
  const styleOverride = document.createElement("style");
  styleOverride.textContent = `
    #code-output pre {
      background-color: #e8f4ff !important;
      border-left: 3px solid #4299e1 !important;
      color: #2c5282 !important;
      padding: 8px 12px !important;
      border-radius: 8px !important;
      margin-bottom: 10px !important;
    }
  `;
  document.head.appendChild(styleOverride);
  console.log("Added direct style override for parameter requests");

  // Removed conversation variable and model-related elements/logic

  // PDF upload and processing function
  function processPDF(file, additionalText = "") {
    console.log("Processing PDF:", file.name);

    // Create FormData object
    const formData = new FormData();
    formData.append("file", file);

    // Add user input if provided
    if (additionalText) {
      formData.append("user_input", additionalText);
    }

    // Add current session_id if available for continuity
    if (currentSessionId) {
      formData.append("session_id", currentSessionId);
    }

    // Show loading indicator
    const loadingIndicator = document.createElement("div");
    loadingIndicator.id = "pdf-processing";
    loadingIndicator.className = "text-center p-4";
    loadingIndicator.innerHTML = `
      <div class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing PDF...
      </div>
    `;
    codeOutput.appendChild(loadingIndicator);

    // Call the API endpoint
    fetch("/api/process-pdf", {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Remove loading indicator
        document.getElementById("pdf-processing")?.remove();

        if (data.success) {
          // Store session_id for conversation continuity
          if (data.session_id) {
            updateSessionIndicator(data.session_id);
          }

          // Display the analysis result
          displayMessage(data.message, "bot");
        } else {
          // Display error
          displayError(`Error processing PDF: ${data.message}`);
        }
      })
      .catch((error) => {
        // Remove loading indicator
        document.getElementById("pdf-processing")?.remove();

        console.error("Error:", error);
        displayError(`Error processing PDF: ${error.message}`);
      });
  }

  // Image upload and processing function
  function processImage(file, additionalText = "") {
    console.log("Processing Image:", file.name);

    // Create FormData object
    const formData = new FormData();
    formData.append("file", file);

    // Add user input if provided
    if (additionalText) {
      formData.append("user_input", additionalText);
    }

    // Add current session_id if available for continuity
    if (currentSessionId) {
      formData.append("session_id", currentSessionId);
    }

    // Show loading indicator
    const loadingIndicator = document.createElement("div");
    loadingIndicator.id = "image-processing";
    loadingIndicator.className = "text-center p-4";
    loadingIndicator.innerHTML = `
      <div class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing Image...
      </div>
    `;
    codeOutput.appendChild(loadingIndicator);

    // Call the API endpoint
    fetch("/api/process-image", {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Remove loading indicator
        document.getElementById("image-processing")?.remove();

        if (data.success) {
          // Store session_id for conversation continuity
          if (data.session_id) {
            updateSessionIndicator(data.session_id);
          }

          // Display the analysis result
          displayMessage(data.message, "bot");
        } else {
          // Display error
          displayError(`Error processing image: ${data.message}`);
        }
      })
      .catch((error) => {
        // Remove loading indicator
        document.getElementById("image-processing")?.remove();

        console.error("Error:", error);
        displayError(`Error processing image: ${error.message}`);
      });
  }

  // Multi-file processing function
  function processMultiFile(pdfFile, imageFile, additionalText = "") {
    console.log("Processing multiple files:", {
      pdf: pdfFile?.name,
      image: imageFile?.name,
    });

    // Create FormData object
    const formData = new FormData();

    if (pdfFile) {
      formData.append("pdf_file", pdfFile);
    }
    if (imageFile) {
      formData.append("image_file", imageFile);
    }

    // Add user input if provided
    if (additionalText) {
      formData.append("user_input", additionalText);
    }

    // Add current session_id if available for continuity
    if (currentSessionId) {
      formData.append("session_id", currentSessionId);
    }

    // Show loading indicator
    const loadingIndicator = document.createElement("div");
    loadingIndicator.id = "multi-file-processing";
    loadingIndicator.className = "text-center p-4";
    loadingIndicator.innerHTML = `
      <div class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing Files...
      </div>
    `;
    codeOutput.appendChild(loadingIndicator);

    // Call the API endpoint
    fetch("/api/process-multi-file", {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Remove loading indicator
        document.getElementById("multi-file-processing")?.remove();

        // Display results based on what was processed
        let message = "";

        if (data.pdf_result) {
          message += `PDF Analysis: ${
            data.pdf_result.success
              ? data.pdf_result.message
              : "Failed - " + data.pdf_result.message
          }\n\n`;
        }

        if (data.image_result) {
          message += `Image Analysis: ${
            data.image_result.success
              ? data.image_result.message
              : "Failed - " + data.image_result.message
          }\n\n`;
        }

        if (data.combined_result) {
          message += `Combined Analysis: ${data.combined_result.message}`;
        }

        if (message) {
          displayMessage(message.trim(), "bot");
        } else {
          displayError("No results received from file processing");
        }
      })
      .catch((error) => {
        // Remove loading indicator
        document.getElementById("multi-file-processing")?.remove();

        console.error("Error:", error);
        displayError(`Error processing files: ${error.message}`);
      });
  }

  // Refresh button event listener (to reset state)
  refreshBtn.addEventListener("click", function () {
    console.log(
      "%c🔄 CLIENT: Refresh button pressed",
      "color: blue; font-weight: bold"
    );

    // Clear session
    updateSessionIndicator(null);

    // Clear code output area
    codeOutput.innerHTML = `
      <p class="text-gray-400">Enter a description to generate FreeCAD code.</p>
    `;

    // Hide STEP viewer button
    viewStepBtn.classList.add("hidden");

    // Reset other state
    latestCode = null;
    selectedPdfFile = null;
    selectedImageFile = null;
    updateFileDisplay();

    // Call the refresh API
    fetch("/api/refresh_chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => {
        if (!response.ok) {
          console.error(
            "%c❌ CLIENT: Server responded with error status: " +
              response.status,
            "color: red; font-weight: bold"
          );
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log(
          "%c✅ CLIENT: Chat refreshed successfully",
          "color: green; font-weight: bold"
        );
        console.log("Response data:", data);
      })
      .catch((error) => {
        console.error(
          "%c❌ CLIENT: Error refreshing chat: " + error,
          "color: red; font-weight: bold"
        );
      });
  });

  // Unified file attachment button event listener
  attachFileBtn.addEventListener("click", function () {
    console.log("CLIENT: Attach File button pressed");
    // Create a hidden file input element
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = ".pdf,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.webp"; // Accept both PDF and image formats
    fileInput.style.display = "none";

    // Append to the body to make it interactable (though hidden)
    document.body.appendChild(fileInput);

    // Listen for file selection
    fileInput.addEventListener("change", function (event) {
      const file = event.target.files[0];
      if (file) {
        console.log("CLIENT: Selected file:", file.name, file.type, file.size);

        // Determine file type
        const isPDF =
          file.type === "application/pdf" ||
          file.name.toLowerCase().endsWith(".pdf");
        const isImage =
          file.type.startsWith("image/") ||
          /\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/i.test(file.name);

        if (!isPDF && !isImage) {
          displayError("Please select a valid PDF or image file.");
          document.body.removeChild(fileInput);
          return;
        }

        // Validate file size
        const maxSize = 20 * 1024 * 1024; // 20MB
        if (file.size > maxSize) {
          displayError(
            `File size (${(file.size / 1024 / 1024).toFixed(
              2
            )}MB) exceeds maximum allowed size (20MB)`
          );
          document.body.removeChild(fileInput);
          return;
        }

        // Store the file based on type
        if (isPDF) {
          selectedPdfFile = file;
        } else if (isImage) {
          selectedImageFile = file;
        }

        // Update file display
        updateFileDisplay();

        // Enable send button
        sendBtn.disabled = false;
        userInput.placeholder = "Add comments or send";
      } else {
        userInput.placeholder = "Enter CAD description...";
      }
      // Clean up the input element after use
      document.body.removeChild(fileInput);
    });

    // Programmatically click the hidden file input
    fileInput.click();
  });

  // Function to update file display
  function updateFileDisplay() {
    let displayHTML = "";

    if (selectedPdfFile) {
      displayHTML += `
        <span class="inline-flex items-center bg-indigo-100 text-indigo-700 text-xs font-medium pl-2.5 pr-1 py-0.5 rounded-full mr-2 mb-1">
          <i class="fas fa-file-pdf mr-1.5"></i>
          ${selectedPdfFile.name}
          <button type="button" class="remove-file-btn ml-1.5 flex-shrink-0 inline-flex items-center justify-center h-4 w-4 text-indigo-400 hover:text-indigo-600 focus:outline-none" data-file-type="pdf">
            <i class="fas fa-times-circle"></i>
          </button>
        </span>`;
    }

    if (selectedImageFile) {
      displayHTML += `
        <span class="inline-flex items-center bg-green-100 text-green-700 text-xs font-medium pl-2.5 pr-1 py-0.5 rounded-full mr-2 mb-1">
          <i class="fas fa-image mr-1.5"></i>
          ${selectedImageFile.name}
          <button type="button" class="remove-file-btn ml-1.5 flex-shrink-0 inline-flex items-center justify-center h-4 w-4 text-green-400 hover:text-green-600 focus:outline-none" data-file-type="image">
            <i class="fas fa-times-circle"></i>
          </button>
        </span>`;
    }

    selectedFileInfo.innerHTML = displayHTML;

    // Add event listeners for remove buttons
    const removeButtons = selectedFileInfo.querySelectorAll(".remove-file-btn");
    removeButtons.forEach((button) => {
      button.addEventListener("click", function () {
        const fileType = this.getAttribute("data-file-type");
        if (fileType === "pdf") {
          selectedPdfFile = null;
        } else if (fileType === "image") {
          selectedImageFile = null;
        }

        updateFileDisplay();

        // Update UI state
        if (!selectedPdfFile && !selectedImageFile) {
          userInput.placeholder = "Enter CAD description...";
          sendBtn.disabled = userInput.value.trim() === "";
        }
      });
    });
  }

  // Keep selected file info visible if user starts typing in the textarea
  userInput.addEventListener("input", function () {
    // The selectedFileInfo should only be cleared upon successful submission with files
    // No action needed here to clear the display when typing
    sendBtn.disabled =
      this.value.trim() === "" && !selectedPdfFile && !selectedImageFile;
  });

  // Toggle chat widget
  chatToggle.addEventListener("click", function () {
    chatbotWidget.classList.toggle("active");
    chatToggle.classList.toggle("hidden");
  });

  // Close chat
  closeChat.addEventListener("click", function () {
    chatbotWidget.classList.remove("active");
    chatToggle.classList.remove("hidden");
  });

  // Minimize chat
  minimizeChat.addEventListener("click", function () {
    chatbotWidget.classList.remove("active");
    chatToggle.classList.remove("hidden");
  });

  // Edit mode toggle
  editModeToggle.addEventListener("change", function () {
    isEditMode = this.checked;
    if (isEditMode) {
      editModeIndicator.classList.remove("hidden");
      userInput.placeholder = "Enter modification request...";

      // Disable edit mode if no code has been generated yet
      if (!latestCode) {
        // Try to load the most recent code from history if available
        fetch("/api/chat-history?limit=1")
          .then((response) => response.json())
          .then((data) => {
            if (data.history && data.history.length > 0) {
              // Use the most recent code from history
              const mostRecentEntry = data.history[0];
              displayCode(mostRecentEntry.generated_code);
              latestCode = mostRecentEntry.generated_code;

              // Show notification
              const notification = document.createElement("div");
              notification.className =
                "text-sm text-green-600 mt-1 mb-2 fade-out";
              notification.textContent =
                "Loaded most recent code from history for editing.";
              notification.style.animation = "fadeOut 3s forwards";

              // Add the notification before the form
              chatForm.parentNode.insertBefore(notification, chatForm);

              // Remove the notification after 3 seconds
              setTimeout(() => {
                notification.remove();
              }, 3000);
            } else {
              // No history available, disable edit mode
              alert(
                "No code has been generated yet. Generate code first before using edit mode."
              );
              editModeToggle.checked = false;
              isEditMode = false;
              editModeIndicator.classList.add("hidden");
              userInput.placeholder = "Enter CAD description...";
            }
          })
          .catch((error) => {
            console.error("Error loading history for edit mode:", error);
            // Fallback to disabling edit mode
            alert(
              "No code has been generated yet. Generate code first before using edit mode."
            );
            editModeToggle.checked = false;
            isEditMode = false;
            editModeIndicator.classList.add("hidden");
            userInput.placeholder = "Enter CAD description...";
          });
      } else {
        // Show a notification about automatic bounding box detection
        const notification = document.createElement("div");
        notification.className = "text-sm text-gray-600 mt-1 mb-2 fade-out"; // Use fade-out class
        notification.textContent =
          "Edit mode enabled. Bounding box will be detected automatically if not specified.";
        notification.style.animation = "fadeOut 3s forwards"; // Add fade-out animation

        // Add the notification before the form
        chatForm.parentNode.insertBefore(notification, chatForm);

        // Remove the notification after 3 seconds
        setTimeout(() => {
          notification.remove();
        }, 3000);
      }
    } else {
      editModeIndicator.classList.add("hidden");
      userInput.placeholder = "Enter CAD description...";
    }
  });

  // Modify Form submission to handle multiple file types
  chatForm.addEventListener("submit", function (e) {
    e.preventDefault();
    const message = userInput.value.trim();

    // Check if files are selected
    if (selectedPdfFile || selectedImageFile) {
      // Determine which processing function to use
      if (selectedPdfFile && selectedImageFile) {
        // Process both files together
        processMultiFile(selectedPdfFile, selectedImageFile, message);
      } else if (selectedPdfFile) {
        // Process PDF only
        processPDF(selectedPdfFile, message);
      } else if (selectedImageFile) {
        // Process image only
        processImage(selectedImageFile, message);
      }

      // Reset file selections
      selectedFileInfo.innerHTML = "";
      selectedPdfFile = null;
      selectedImageFile = null;

      // Clear input field
      userInput.value = "";
      userInput.placeholder = "Enter CAD description...";
      sendBtn.disabled = true;

      return; // Return early to avoid standard text processing
    }

    if (message) {
      // Clear previous code output
      codeOutput.innerHTML = "";

      // Show typing indicator (repurposed for processing indicator)
      showTypingIndicator();

      // Send request to API
      fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: message,
          is_edit_request: isEditMode,
          session_id: currentSessionId, // Include session_id for conversation continuity
        }),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          // Remove typing indicator
          removeTypingIndicator();

          console.log("Server response:", data); // Log the response for debugging

          // Store session_id from chat response for continuity
          if (data.session_id) {
            // Check if session_id exists before updating
            updateSessionIndicator(data.session_id);
          }

          // The primary content from the server is in data.chat_response
          if (data.chat_response) {
            displayMessage(data.chat_response, "bot"); // This will handle parameter requests, success messages, etc.

            // If the response indicates successful generation (e.g., by providing export links),
            // make the "View STEP" button visible.
            // The actual FreeCAD code is not sent in this response, so `latestCode` is not updated here.
            // `displayCode` is not called here for new code.
            if (data.obj_export || data.step_export) {
              console.log(
                "Export files available, ensuring STEP viewer button is visible."
              );
              if (viewStepBtn) {
                // Ensure viewStepBtn is defined and accessible
                viewStepBtn.classList.remove("hidden");
              }
            }
          }
          // If data.chat_response is missing, then the server's response format is truly unexpected
          // according to the ChatResponse schema where chat_response is a required field.
          else {
            console.warn(
              "Unexpected response format: data.chat_response is missing.",
              data
            );
            displayError(
              "Received an unexpected response format from the server (chat_response is missing)."
            );
          }

          sendBtn.disabled = false;
        })
        .catch((error) => {
          console.error("Error:", error);
          removeTypingIndicator();

          // Provide more detailed error information
          let errorMessage =
            "Sorry, an error occurred while connecting to the server.";
          if (error.message) {
            errorMessage += ` Details: ${error.message}`;
          }

          displayError(errorMessage);
          sendBtn.disabled = false;
        });
    }
  });

  // Show typing indicator (repurposed)
  function showTypingIndicator() {
    const typingDiv = document.createElement("div");
    typingDiv.className = "message flex space-x-2"; // Keep some styling
    typingDiv.id = "typing-indicator";
    typingDiv.innerHTML = `
            <div class="flex-shrink-0 w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
                <i class="fas fa-robot text-indigo-600 text-xs"></i>
            </div>
            <div class="bg-gray-100 p-2 rounded-lg max-w-[80%]">
                <div class="typing-indicator">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;

    // Append to code output area instead of chat messages
    codeOutput.appendChild(typingDiv);
    typingDiv.scrollIntoView({ behavior: "smooth" });
  }

  // Remove typing indicator
  function removeTypingIndicator() {
    const typingIndicator = document.getElementById("typing-indicator");
    if (typingIndicator) {
      typingIndicator.remove();
    }
  }

  // New function to display generated code
  function displayCode(code) {
    const codeBlock = document.createElement("pre");
    const codeElement = document.createElement("code");

    // Check if this is a parameter request and style it accordingly
    if (
      code.includes("I need some more information") ||
      code.includes("Please provide the following parameters") ||
      code.includes("Sheet length") ||
      code.includes("Hole type")
    ) {
      console.log(
        "Detected parameter request in displayCode, applying special styling"
      );
      codeBlock.style.backgroundColor = "#e8f4ff";
      codeBlock.style.borderLeft = "3px solid #4299e1";
      codeBlock.style.color = "#2c5282";
      codeBlock.style.padding = "8px 12px";
      codeBlock.style.borderRadius = "8px";
      codeBlock.style.maxWidth = "100%";
      codeBlock.style.marginBottom = "10px";
      codeBlock.classList.add("parameter-request");
    }

    codeElement.textContent = code;
    codeBlock.appendChild(codeElement);
    codeOutput.appendChild(codeBlock);
    codeBlock.scrollIntoView({ behavior: "smooth" });

    // Show STEP viewer button if this is actual FreeCAD code (not parameter request)
    if (
      !code.includes("I need some more information") &&
      !code.includes("Please provide the following parameters") &&
      code.includes("import FreeCAD")
    ) {
      console.log("Showing STEP viewer button - FreeCAD code detected");
      viewStepBtn.classList.remove("hidden");
    }
  }

  // Function to display errors
  function displayError(error) {
    const errorDiv = document.createElement("div");
    errorDiv.className =
      "bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded";

    // Create a heading for the error
    const errorHeading = document.createElement("div");
    errorHeading.className = "font-bold";
    errorHeading.textContent = "Error";
    errorDiv.appendChild(errorHeading);

    // Add the error message
    const errorMessage = document.createElement("div");
    errorMessage.textContent = error;
    errorDiv.appendChild(errorMessage);

    // Add to code output
    codeOutput.appendChild(errorDiv);
    errorDiv.scrollIntoView({ behavior: "smooth" });

    // Log the error to console for debugging
    console.error("Error displayed to user:", error);
  }

  // Chat history functions

  // Function to load chat history
  function loadChatHistory() {
    // Clear previous history
    chatHistory.innerHTML = "";

    // Show loading indicator
    const loadingIndicator = document.createElement("div");
    loadingIndicator.className = "text-center text-gray-500 py-2";
    loadingIndicator.textContent = "Loading chat history...";
    chatHistory.appendChild(loadingIndicator);

    // Fetch chat history from API
    fetch("/api/chat-history")
      .then((response) => response.json())
      .then((data) => {
        // Remove loading indicator
        chatHistory.innerHTML = "";

        // Check if history exists
        if (data.history && data.history.length > 0) {
          // Display history entries
          data.history.forEach((entry) => {
            displayHistoryEntry(entry);
          });
        } else {
          // Display empty message
          const emptyMessage = document.createElement("p");
          emptyMessage.className = "text-gray-400";
          emptyMessage.textContent = "No chat history found.";
          chatHistory.appendChild(emptyMessage);
        }
      })
      .catch((error) => {
        console.error("Error loading chat history:", error);
        chatHistory.innerHTML = "";
        const errorMessage = document.createElement("p");
        errorMessage.className = "text-red-500";
        errorMessage.textContent = "Failed to load chat history.";
        chatHistory.appendChild(errorMessage);
      });
  }

  // Function to display a history entry
  function displayHistoryEntry(entry) {
    const entryDiv = document.createElement("div");
    entryDiv.className = "mb-4 p-3 bg-white rounded shadow-sm";

    // Format timestamp
    const timestamp = new Date(entry.timestamp);
    const formattedDate = timestamp.toLocaleDateString();
    const formattedTime = timestamp.toLocaleTimeString();

    // Create entry content
    entryDiv.innerHTML = `
      <div class="flex justify-between items-start mb-2">
        <div class="font-medium ${
          entry.is_edit_request ? "text-indigo-600" : "text-gray-700"
        }">
          ${
            entry.is_edit_request
              ? '<i class="fas fa-pencil-alt mr-1"></i> Edit Request'
              : '<i class="fas fa-comment mr-1"></i> New Shape'
          }
        </div>
        <div class="text-xs text-gray-500">${formattedDate} ${formattedTime}</div>
      </div>
      <div class="text-sm text-gray-600 mb-2">${entry.user_message}</div>
      <div class="text-xs text-gray-500 flex justify-end">
        <button class="use-code-btn text-indigo-600 hover:text-indigo-800 transition">
          <i class="fas fa-code mr-1"></i> Use This Code
        </button>
      </div>
    `;

    // Add event listener to the "Use This Code" button
    const useCodeBtn = entryDiv.querySelector(".use-code-btn");
    useCodeBtn.addEventListener("click", function () {
      // Display the code in the code output area
      displayCode(entry.generated_code);
      // Store the code as the latest code
      latestCode = entry.generated_code;

      // Send a request to update the server-side latest code
      fetch("/api/update-latest-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: entry.generated_code,
        }),
      })
        .then((response) => response.json())
        .catch((error) => console.error("Error updating latest code:", error));

      // Enable edit mode automatically
      if (!isEditMode) {
        editModeToggle.checked = true;
        isEditMode = true;
        editModeIndicator.classList.remove("hidden");
        userInput.placeholder = "Enter modification request...";
      }

      // Show notification
      const notification = document.createElement("div");
      notification.className = "text-sm text-green-600 mt-1 mb-2 fade-out";
      notification.textContent =
        "Code loaded from history. Edit mode enabled for modifications.";
      notification.style.animation = "fadeOut 3s forwards";

      // Add the notification before the code output
      codeOutput.parentNode.insertBefore(notification, codeOutput);

      // Remove the notification after 3 seconds
      setTimeout(() => {
        notification.remove();
      }, 3000);

      // Focus on the input field for immediate editing
      userInput.focus();
    });

    // Add the entry to the chat history
    chatHistory.appendChild(entryDiv);
  }

  // Function to clear chat history
  function clearChatHistory() {
    // Show confirmation dialog
    if (
      confirm(
        "Are you sure you want to clear all chat history? This action cannot be undone."
      )
    ) {
      // Send request to API
      fetch("/api/chat-history", {
        method: "DELETE",
      })
        .then((response) => response.json())
        .then((data) => {
          // Reload chat history
          loadChatHistory();

          // Show notification
          const notification = document.createElement("div");
          notification.className = "text-sm text-green-600 mt-1 mb-2 fade-out";
          notification.textContent = "Chat history cleared successfully.";
          notification.style.animation = "fadeOut 3s forwards";

          // Add the notification before the chat history
          chatHistory.parentNode.insertBefore(notification, chatHistory);

          // Remove the notification after 3 seconds
          setTimeout(() => {
            notification.remove();
          }, 3000);
        })
        .catch((error) => {
          console.error("Error clearing chat history:", error);
          alert("Failed to clear chat history. Please try again.");
        });
    }
  }

  // Add event listener to clear history button
  clearHistoryBtn.addEventListener("click", clearChatHistory);

  // Load chat history when the page loads
  loadChatHistory();

  // Update loadChatHistory after successful chat submission
  const originalFetch = window.fetch;
  window.fetch = function () {
    const result = originalFetch.apply(this, arguments);

    // Check if this is a chat API call
    if (arguments[0] === "/api/chat" && arguments[1]?.method === "POST") {
      result.then((response) => {
        // Reload chat history after a short delay to ensure the new entry is saved
        setTimeout(loadChatHistory, 1000);
        return response;
      });
    }

    return result;
  };

  // Removed saved shapes logic
  // Function to display messages
  function displayMessage(message, sender) {
    // Create a new message element
    const messageElement = document.createElement("div");
    messageElement.className = `message ${sender}`;

    // Check if this is a message asking for parameters
    // More robust detection of parameter request messages
    if (sender === "bot") {
      console.log("Creating bot message with class:", messageElement.className); // Debug log
      // Apply bot styles directly
      messageElement.style.backgroundColor = "#e8f4ff";
      messageElement.style.borderLeft = "3px solid #4299e1";
      messageElement.style.color = "#2c5282";
      messageElement.style.padding = "8px 12px";
      messageElement.style.borderRadius = "8px";
      messageElement.style.maxWidth = "90%";
      messageElement.style.marginBottom = "10px";
      // Extract questions from the message if they're in a list format
      const questions = [];
      const lines = message.split("\n");

      // Look for patterns that indicate this is a parameter request
      const isParameterRequest =
        (message.includes("missing") &&
          (message.includes("parameter") || message.includes("information"))) ||
        (message.includes("provide") && message.includes("following")) ||
        (message.includes("need") &&
          (message.includes("information") || message.includes("parameter"))) ||
        (message.includes("sheet") && message.includes("thickness")) ||
        (message.includes("hole") &&
          (message.includes("radius") || message.includes("pattern")));

      // Count how many numbered list items we have
      let numberedItems = 0;

      for (const line of lines) {
        // Look for numbered list items (e.g., "1. What is the length?")
        const match = line.match(/^\s*\d+\.\s+(.+)$/);
        if (match) {
          questions.push(match[1].trim());
          numberedItems++;
        }
      }

      // Special case for the exact format shown in the screenshot
      if (
        message.includes("Please provide the following missing information") &&
        (message.includes("Sheet thickness") ||
          message.includes("thickness")) &&
        (message.includes("Hole radius") || message.includes("radius")) &&
        (message.includes("Hole pattern") || message.includes("pattern"))
      ) {
        console.log("Detected specific perforated sheet parameter request");

        // Extract explanation if present
        let explanation = "";
        if (
          message.includes("Need") &&
          message.includes("to model the sheet")
        ) {
          const lines = message.split("\n");
          for (const line of lines) {
            if (line.includes("Need") && line.includes("to model the sheet")) {
              explanation = line.trim();
              break;
            }
          }
        }

        // Display the parameter table
        displayParameterTable([
          "Sheet thickness",
          "Hole radius",
          "Hole pattern",
        ]);

        // If we found an explanation, display it separately
        if (explanation) {
          setTimeout(() => {
            displayMessage(`<i>${explanation}</i>`, "bot-explanation");
          }, 500);
        }

        return;
      }

      // If we have a parameter request message with numbered items, display as a table
      if ((isParameterRequest || numberedItems >= 2) && questions.length > 0) {
        console.log(
          "Detected parameter request, displaying table with questions:",
          questions
        );
        displayParameterTable(questions);
        return;
      }
    }

    // Format the message with line breaks (for non-parameter messages)
    messageElement.innerHTML = message.replace(/\n/g, "<br>");

    // Add to code output container
    codeOutput.appendChild(messageElement);

    // Also add to chat messages container for compatibility
    const chatContainer = document.getElementById("chat-messages");
    if (chatContainer) {
      const chatMessageElement = messageElement.cloneNode(true);
      chatContainer.appendChild(chatMessageElement);
    }

    // Force style refresh (try to trigger reflow)
    messageElement.offsetHeight;

    // Scroll to the new message
    messageElement.scrollIntoView({ behavior: "smooth" });
  }

  // Function to display a table of missing parameters
  function displayParameterTable(questions) {
    console.log("Creating parameter table with questions:", questions);

    // Instead of creating a parameter table, display as normal chat messages
    const messageElement = document.createElement("div");
    messageElement.className = "message bot parameter-message";

    // Apply bot styles directly to ensure they take effect
    messageElement.style.backgroundColor = "#e8f4ff";
    messageElement.style.borderLeft = "3px solid #4299e1";
    messageElement.style.color = "#2c5282";
    messageElement.style.padding = "8px 12px";
    messageElement.style.borderRadius = "8px";
    messageElement.style.maxWidth = "90%";
    messageElement.style.marginBottom = "10px";

    // Create a list of questions
    let messageContent =
      "I need some more information. Please provide the following parameters:<br><ul>";
    questions.forEach((question) => {
      messageContent += `<li>${question}</li>`;
    });
    messageContent += "</ul>";

    messageElement.innerHTML = messageContent;
    codeOutput.appendChild(messageElement);

    // Log for debugging
    console.log("Added parameter message with styles:", {
      backgroundColor: messageElement.style.backgroundColor,
      borderLeft: messageElement.style.borderLeft,
      element: messageElement,
    });

    messageElement.scrollIntoView({ behavior: "smooth" });

    /* Original table code - commented out but preserved
    // Create container for the parameter form
    const formContainer = document.createElement("div");
    formContainer.className =
      "parameter-form bg-white border border-indigo-200 rounded-lg p-4 mb-4";
    formContainer.id = "parameter-form-container";

    // Create form element
    const form = document.createElement("form");
    form.className = "space-y-4";

    // Create table for parameters
    const table = document.createElement("table");
    table.className = "w-full border-collapse";

    // Add table header (with Vietnamese translation)
    const thead = document.createElement("thead");
    const headerRow = document.createElement("tr");
    const isVietnamese = document.documentElement.lang === "vi";
    headerRow.innerHTML = `
      <th class="text-left py-2 px-3 bg-indigo-100 border-b-2 border-indigo-200">Parameter</th>
      <th class="text-left py-2 px-3 bg-indigo-100 border-b-2 border-indigo-200">Value</th>
    `;
    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Add table body
    const tbody = document.createElement("tbody");

    // Add a row for each missing parameter
    const cleanedQuestions = questions.map((q) => {
      // Remove trailing punctuation and question marks
      let cleaned = q.replace(/[?:.,;!]+$/, "").trim();
      // If the question is just "Sheet thickness", "Hole radius", etc., add a label
      if (
        cleaned.toLowerCase().includes("thickness") &&
        !cleaned.includes("=")
      ) {
        cleaned = "Sheet thickness";
      }
      if (cleaned.toLowerCase().includes("radius") && !cleaned.includes("=")) {
        cleaned = "Hole radius";
      }
      if (cleaned.toLowerCase().includes("pattern") && !cleaned.includes("=")) {
        cleaned = "Hole pattern";
      }
      return cleaned;
    });

    cleanedQuestions.forEach((question, index) => {
      const row = document.createElement("tr");
      row.className = index % 2 === 0 ? "bg-gray-50" : "bg-white";

      // Parameter name cell
      const nameCell = document.createElement("td");
      nameCell.className = "py-2 px-3 border-b border-gray-200";
      nameCell.textContent = question;

      // Parameter value cell
      const valueCell = document.createElement("td");
      valueCell.className = "py-2 px-3 border-b border-gray-200";

      // Create input for parameter value (with Vietnamese translation)
      const input = document.createElement("input");
      input.type = "text";
      input.name = `param_${index}`;
      input.className =
        "w-full border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500";
      input.placeholder = "Enter value";
      input.required = true;

      valueCell.appendChild(input);

      // Add cells to row
      row.appendChild(nameCell);
      row.appendChild(valueCell);

      // Add row to table body
      tbody.appendChild(row);
    });

    table.appendChild(tbody);
    form.appendChild(table);

    // Add submit button (with Vietnamese translation)
    const submitButton = document.createElement("button");
    submitButton.type = "submit";
    submitButton.className =
      "w-full bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700 transition";
    submitButton.textContent = "Submit Parameters";
    form.appendChild(submitButton);

    // Add form to container
    formContainer.appendChild(form);

    // Add form container to code output
    codeOutput.appendChild(formContainer);
    formContainer.scrollIntoView({ behavior: "smooth" });

    // Add event listener for form submission
    form.addEventListener("submit", function (e) {
      e.preventDefault();

      // Collect all parameter values
      const paramValues = [];
      cleanedQuestions.forEach((question, index) => {
        const input = form.querySelector(`input[name="param_${index}"]`);
        // Use the original question text for better context
        paramValues.push(`${question}: ${input.value}`);
      });

      // Join all parameter values into a single message
      const combinedMessage = paramValues.join(", ");

      // Remove the form
      formContainer.remove();

      // Display a message showing the submitted parameters (with Vietnamese translation)
      const submittedMessage = document.createElement("div");
      submittedMessage.className = "message user";
      submittedMessage.innerHTML = `<strong>Submitted Parameters:</strong> ${combinedMessage}`;
      codeOutput.appendChild(submittedMessage);

      // Send the combined parameters to the server
      // Clear previous code output except for the submitted message
      const allElements = Array.from(codeOutput.children);
      allElements.forEach((el) => {
        if (el !== submittedMessage) {
          el.remove();
        }
      });

      // Show typing indicator
      showTypingIndicator();

      // Send request to API
      fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: combinedMessage,
          is_edit_request: isEditMode,
        }),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          // Remove typing indicator
          removeTypingIndicator();

          console.log("Server response:", data); // Log the response for debugging

          // Check if there's a message asking for more parameters
          if (data.message) {
            console.log(
              "Received message from server after parameter submission:",
              data.message
            );

            // Display the message asking for more parameters
            displayMessage(data.message, "bot");

            // If there's an explanation, display it as well
            if (data.explanation) {
              console.log(
                "Received explanation from server after parameter submission:",
                data.explanation
              );
              setTimeout(() => {
                displayMessage(`<i>${data.explanation}</i>`, "bot-explanation");
              }, 500);
            }
          }
          // Display generated code if available
          else if (data.code) {
            displayCode(data.code);
            // Store the latest code
            latestCode = data.code;
          }
          // Display error if present
          else if (data.error) {
            displayError(data.error);
          }
          // Handle unexpected response format
          else {
            console.warn("Unexpected response format:", data);
            displayError(
              "Received an unexpected response format from the server."
            );
          }

          sendBtn.disabled = false;
        })
        .catch((error) => {
          console.error("Error:", error);
          removeTypingIndicator();

          // Provide more detailed error information
          let errorMessage =
            "Sorry, an error occurred while connecting to the server.";
          if (error.message) {
            errorMessage += ` Details: ${error.message}`;
          }

          displayError(errorMessage);
          sendBtn.disabled = false;
        });
    });
    */
  }

  // Add CSS for messages and explanations
  const style = document.createElement("style");
  style.textContent = `
    .bot-explanation {
      font-size: 0.9em;
      color: #666;
      background-color: #f5f5f5;
      border-left: 3px solid #ccc;
      margin-left: 20px;
      padding: 5px 10px;
    }

    #code-output .message {
      margin-bottom: 10px;
      padding: 8px 12px;
      border-radius: 8px;
      max-width: 90%;
    }

    #code-output .message.bot {
      background-color: #e8f4ff !important;
      border-left: 3px solid #4299e1 !important;
      color: #2c5282 !important;
    }

    /* Parameter table styles */
    .parameter-form {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      background-color: #f8faff;
      border: 2px solid #4299e1;
      animation: highlight-form 1s ease-in-out;
    }

    @keyframes highlight-form {
      0% { transform: scale(0.98); opacity: 0.8; }
      50% { transform: scale(1.02); opacity: 1; }
      100% { transform: scale(1); }
    }

    .parameter-form:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
    }

    .parameter-form table {
      border-radius: 4px;
      overflow: hidden;
      width: 100%;
    }

    .parameter-form th {
      background-color: #4299e1;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.85rem;
      letter-spacing: 0.05em;
    }

    .parameter-form input {
      border: 1px solid #cbd5e0;
      padding: 0.5rem 0.75rem;
      border-radius: 0.375rem;
      width: 100%;
      transition: all 0.2s;
    }

    .parameter-form input:focus {
      border-color: #4f46e5;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25);
      outline: none;
    }

    .parameter-form button {
      font-weight: 600;
      transition: all 0.2s ease;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-top: 1rem;
    }

    .parameter-form button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  `;
  document.head.appendChild(style);

  // Add CSS for PDF processing
  const pdfStyles = document.createElement("style");
  pdfStyles.textContent = `
    #pdf-processing {
      margin: 20px 0;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.6;
      }
      100% {
        opacity: 1;
      }
    }
  `;
  document.head.appendChild(pdfStyles);

  // STEP viewer button event listener
  viewStepBtn.addEventListener("click", function () {
    console.log("CLIENT: View STEP button pressed");
    launchStepViewer();
  });

  // Function to launch STEP viewer
  function launchStepViewer() {
    console.log("Launching STEP viewer...");

    // Show loading indicator
    const originalText = viewStepBtn.innerHTML;
    viewStepBtn.disabled = true;
    viewStepBtn.innerHTML =
      '<i class="fas fa-spinner fa-spin mr-2"></i>Launching...';

    fetch("/api/launch-step-viewer", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        if (data.success) {
          console.log("STEP viewer launched successfully");
          // Show success message briefly
          viewStepBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Launched!';
          setTimeout(() => {
            viewStepBtn.innerHTML = originalText;
            viewStepBtn.disabled = false;
          }, 2000);
        } else {
          throw new Error(data.message || "Failed to launch STEP viewer");
        }
      })
      .catch((error) => {
        console.error("Error launching STEP viewer:", error);
        viewStepBtn.innerHTML = originalText;
        viewStepBtn.disabled = false;
        displayError(`Error launching STEP viewer: ${error.message}`);
      });
  }

  // Function to update session indicator
  function updateSessionIndicator(sessionId) {
    if (sessionId) {
      currentSessionId = sessionId;
      sessionIdDisplay.textContent = sessionId;
      sessionIndicator.classList.remove("hidden");
      console.log("Session indicator updated:", sessionId);
    } else {
      sessionIndicator.classList.add("hidden");
      sessionIdDisplay.textContent = "";
      currentSessionId = null;
      console.log("Session indicator hidden");
    }
  }
});
