import sys
import os
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMessageBox, QInputDialog, QPushButton, QVBoxLayout, QHBoxLayout, QDialog
from PyQt5.QtCore import QObject, pyqtSignal, Qt
from PyQt5.QtGui import Q<PERSON>lipboard
from OCC.Core.TopExp import TopExp_Explorer
from OCC.Core.TopAbs import TopAbs_FACE, TopAbs_EDGE
from OCC.Core.TopoDS import topods, topods_Face, topods_Edge
from OCC.Core.AIS import AIS_Shape
from OCC.Core.GProp import GProp_GProps
from OCC.Core.BRepGProp import brepgprop_SurfaceProperties, brepgprop_LinearProperties
from OCC.Core.BRep import BRep_Tool
from OCC.Core.Geom import Geom_Curve
from OCC.Core.GeomAPI import GeomAPI_ProjectPointOnCurve
from OCC.Core.gp import gp_Pnt
from OCC.Extend.DataExchange import read_step_file
from OCC.Core.Bnd import Bnd_Box
from OCC.Core.BRepBndLib import brepbndlib_Add

# Load PyQt5 backend for OCC
from OCC.Display.backend import load_backend
load_backend("pyqt5")
from OCC.Display.qtDisplay import qtViewer3d

class StepViewer(qtViewer3d):
    def __init__(self, parent=None, step_file_path=None):
        super().__init__(parent)
        self.display = self._display
        self.ctx = self.display.Context

        # Store face and edge information
        self.face_map = {}  # ID -> (AIS_Shape, TopoDS_Face)
        self.edge_map = {}  # ID -> (AIS_Shape, TopoDS_Edge)

        # Configure display
        self.ctx.SetDisplayMode(1, True)  # Shaded mode
        self.ctx.Activate(TopAbs_FACE)    # Enable face selection
        self.ctx.Activate(TopAbs_EDGE)    # Enable edge selection

        # Load STEP file if path is provided
        if step_file_path and os.path.exists(step_file_path):
            self.load_step_file(step_file_path)
        else:
            print("No STEP file provided or file does not exist.")

    def load_step_file(self, path):
        print(f"Loading STEP file: {path}")
        try:
            # Read STEP file
            shape = read_step_file(path)
            if shape is None:
                raise ValueError("Unable to read STEP file or file is empty.")

            # Clear previous display content
            self.display.EraseAll()
            self.face_map.clear()
            self.edge_map.clear()

            # Iterate through faces and assign IDs
            face_explorer = TopExp_Explorer(shape, TopAbs_FACE)
            face_id = 0
            while face_explorer.More():
                face = topods.Face(face_explorer.Current())
                ais_face = AIS_Shape(face)
                face_name = f"Face_{face_id}"
                self.face_map[face_name] = (ais_face, face)
                self.ctx.Display(ais_face, False)  # Display face
                face_explorer.Next()
                face_id += 1

            # Iterate through edges and assign IDs
            edge_explorer = TopExp_Explorer(shape, TopAbs_EDGE)
            edge_id = 0
            while edge_explorer.More():
                edge = topods.Edge(edge_explorer.Current())
                ais_edge = AIS_Shape(edge)
                edge_name = f"Edge_{edge_id}"
                self.edge_map[edge_name] = (ais_edge, edge)
                self.ctx.Display(ais_edge, False)  # Display edge
                edge_explorer.Next()
                edge_id += 1

            # Update display
            self.display.FitAll()
            print(f"Successfully loaded {path}. Found {face_id} faces and {edge_id} edges.")

        except Exception as e:
            print(f"Error loading STEP file {path}: {str(e)}")
            QMessageBox.critical(self, "File Load Error", f"Error loading STEP file:\n{path}\n\nDetails: {str(e)}")

    def mousePressEvent(self, event):
        # Call default mouse handling (pan, rotate, zoom, etc.)
        super().mousePressEvent(event)

        # Handle left mouse click to select face/edge
        if event.button() == 1:  # Left click
            x, y = event.x(), event.y()
            self.display.Select(x, y)

            if self.ctx.HasSelectedShape():
                selected_shape = self.ctx.SelectedShape()

                # Check if a face is selected
                if selected_shape.ShapeType() == TopAbs_FACE:
                    selected_face = topods.Face(selected_shape)
                    for face_name, (ais, face) in self.face_map.items():
                        if face.IsEqual(selected_face):
                            # Calculate face properties
                            props = GProp_GProps()
                            brepgprop_SurfaceProperties(face, props)
                            area = props.Mass()
                            centroid = props.CentreOfMass()

                            # Calculate bounding box
                            bbox = Bnd_Box()
                            brepbndlib_Add(face, bbox)
                            xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()

                            # Extract ID from face_name (e.g., "Face_0" -> "0")
                            face_id = face_name.split('_')[1]

                            # Format bounding box string for API
                            bbox_str = f"Selected Face BBox: X[{xmin:.3f}, {xmax:.3f}], Y[{ymin:.3f}, {ymax:.3f}], Z[{zmin:.3f}, {zmax:.3f}]"

                            # Copy to clipboard
                            clipboard = QApplication.clipboard()
                            clipboard.setText(bbox_str)

                            # Print to console
                            print(f"Selected face ID {face_id} - Area: {area:.3f} units²")
                            print(f"Centroid: ({centroid.X():.3f}, {centroid.Y():.3f}, {centroid.Z():.3f})")
                            print(f"Bounding Box: X[{xmin:.3f}, {xmax:.3f}], Y[{ymin:.3f}, {ymax:.3f}], Z[{zmin:.3f}, {zmax:.3f}]")
                            print(f"✅ Bounding box information copied to clipboard!")

                            # Show information in message box with clipboard notification
                            msg = (f"Selected face: {face_name}\n"
                                   f"Area: {area:.3f} units²\n"
                                   f"Centroid: ({centroid.X():.3f}, {centroid.Y():.3f}, {centroid.Z():.3f})\n"
                                   f"Bounding Box: X[{xmin:.3f}, {xmax:.3f}], Y[{ymin:.3f}, {ymax:.3f}], Z[{zmin:.3f}, {zmax:.3f}]\n\n"
                                   f"✅ Bounding box information copied to clipboard!\n"
                                   f"Paste it into your edit request to target this face.")

                            # Display message box
                            QMessageBox.information(self, "Face Information", msg)
                            break

                # Check if an edge is selected
                elif selected_shape.ShapeType() == TopAbs_EDGE:
                    selected_edge = topods.Edge(selected_shape)
                    for edge_name, (ais, edge) in self.edge_map.items():
                        if edge.IsEqual(selected_edge):
                            # Calculate edge length
                            props = GProp_GProps()
                            brepgprop_LinearProperties(edge, props)
                            length = props.Mass()

                            # Get start and end points of the edge
                            curve, u_start, u_end = BRep_Tool.Curve(selected_edge)
                            p1 = curve.Value(u_start)  # Start point
                            p2 = curve.Value(u_end)    # End point

                            # Calculate bounding box for the edge
                            bbox = Bnd_Box()
                            brepbndlib_Add(edge, bbox)
                            xmin, ymin, zmin, xmax, ymax, zmax = bbox.Get()

                            # Format bounding box string for API
                            bbox_str = f"Selected Edge BBox: X[{xmin:.3f}, {xmax:.3f}], Y[{ymin:.3f}, {ymax:.3f}], Z[{zmin:.3f}, {zmax:.3f}]"

                            # Copy to clipboard
                            clipboard = QApplication.clipboard()
                            clipboard.setText(bbox_str)

                            # Create message with edge information
                            msg = (f"Selected edge: {edge_name}\n"
                                   f"Length: {length:.3f} units\n"
                                   f"Start point: ({p1.X():.3f}, {p1.Y():.3f}, {p1.Z():.3f})\n"
                                   f"End point: ({p2.X():.3f}, {p2.Y():.3f}, {p2.Z():.3f})\n"
                                   f"Bounding Box: X[{xmin:.3f}, {xmax:.3f}], Y[{ymin:.3f}, {ymax:.3f}], Z[{zmin:.3f}, {zmax:.3f}]\n\n"
                                   f"✅ Bounding box information copied to clipboard!\n"
                                   f"Paste it into your edit request to target this edge.")

                            # Print to console
                            print(msg)
                            print(f"✅ Bounding box information copied to clipboard!")

                            # Display message box
                            QMessageBox.information(self, "Edge Information", msg)
                            break

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Get STEP file path from command line or use default
    step_file_to_load = sys.argv[1] if len(sys.argv) > 1 else None
    # step_file_to_load = "cad_outputs\Rectangular_prism.step"
    if step_file_to_load and not os.path.exists(step_file_to_load):
        print(f"Error: STEP file not found at {step_file_to_load}")
        QMessageBox.critical(None, "File Load Error", f"STEP file not found:\n{step_file_to_load}")
        step_file_to_load = None

    # Initialize viewer
    viewer = StepViewer(step_file_path=step_file_to_load)
    viewer.setWindowTitle("STEP File Viewer")
    viewer.resize(800, 600)
    viewer.show()
    sys.exit(app.exec_())
