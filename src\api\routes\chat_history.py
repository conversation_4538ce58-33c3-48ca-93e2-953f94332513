"""
API routes for chat history operations.
"""
from fastapi import APIRouter, Depends, Query, HTTPException, Path
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from ...database.database import get_db
from ...schemas.sessions import ChatHistoryResponse, ExportResponse, TessellationResponse
from ... import crud

logger = logging.getLogger("tolery-api")

router = APIRouter(
    prefix="/sessions",
    tags=["chat-history"],
    responses={404: {"description": "Session not found"}},
)


@router.get(
    "/{session_id}/chat",
    summary="Get Chat History",
    response_model=List[ChatHistoryResponse]
)
def get_chat_history(
    session_id: str = Path(..., description="Session ID to retrieve chat history for"),
    limit: int = Query(50, description="Maximum number of messages to return", ge=1),
    page: int = Query(1, description="Page number", ge=1),
    db: Session = Depends(get_db)
):
    """
    Get chat history for a specific session.
    """
    try:
        chat_history = crud.chat_history.get_chats_by_session_id(
            db, session_id, page=page, limit=limit
        )
        return chat_history
    except Exception as e:
        logger.error(f"Error getting chat history for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve chat history")


@router.get(
    "/{session_id}/exports",
    summary="Get Export Links",
    response_model=List[ExportResponse]
)
def get_exports(
    session_id: str = Path(..., description="Session ID to retrieve export links for"),
    export_format: Optional[str] = Query(
        default=None,
        description="Filter by export format (obj, step, dxf)"
    ),
    db: Session = Depends(get_db)
):
    """
    Get export file links for a specific session.
    """
    try:
        exports = crud.chat_history.get_exports_by_session_id(
            db, session_id, export_format
        )
        return exports
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exports for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve exports")


@router.get(
    "/{session_id}/tessellated",
    summary="Get Tessellated Object",
    response_model=TessellationResponse
)
def get_tessellation(
    session_id: str = Path(..., description="Session ID to retrieve tessellated object for"),
    db: Session = Depends(get_db)
):
    """
    Get the tessellated object data for a specific session.
    """
    try:
        tessellation = crud.chat_history.get_tessellation_by_session_id(db, session_id)
        return tessellation
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting tessellation for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve tessellation data") 