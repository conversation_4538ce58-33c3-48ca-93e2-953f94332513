"""
CRUD operations for session management.
"""
from sqlalchemy.orm import Session
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from typing import Optional
import logging
import uuid
import random

from ..models.sessions import Session as SessionModel

logger = logging.getLogger(__name__)


def get_all_sessions(db: Session):
    """
    Get all sessions sorted by ID in descending order (newest first).
    """
    return db.query(SessionModel).order_by(SessionModel.id.desc()).all()


def create_session(db: Session, session_id: Optional[str] = None, name: str = "User Session"):
    """
    Create a new session. If session_id is not provided, generates a new unique ID.
    """
    if not session_id:
        rand_digits = random.randint(100000, 999999)
        rand_uuid = uuid.uuid4().hex[:6]
        session_id = f"session_{rand_uuid}_{rand_digits}"
        logger.info(f"Generated new session ID: {session_id}")
    
    existing_session = db.query(SessionModel).filter(
        SessionModel.session_id == session_id
    ).first()

    if existing_session:
        raise HTTPException(status_code=400, detail="Session ID already exists")

    new_session = SessionModel(session_id=session_id, name=name)
    db.add(new_session)
    db.commit()
    db.refresh(new_session)
    logger.info(f"Successfully created session: {session_id} with name: {name}")
    return new_session


def get_session_by_id(db: Session, session_id: str):
    """
    Get a specific session by ID.
    """
    return db.query(SessionModel).filter(SessionModel.session_id == session_id).first()


def delete_session(db: Session, session_id: str):
    """
    Delete a session permanently.
    """
    session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
        db.delete(session)
        db.commit()
    return {"message": f"Session {session_id} has been permanently deleted"}


def update_session(db: Session, session_id: str, name: Optional[str] = None):
    """
    Update session information.
    """
    session = db.query(SessionModel).filter(SessionModel.session_id == session_id).first()
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    if name:
        session.name = name
    
        db.commit()
    db.refresh(session)
    return session
