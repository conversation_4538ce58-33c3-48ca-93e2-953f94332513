"""
Pydantic schemas for session-related operations.
"""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime


class ChatRequest(BaseModel):
    """
    Schema for chat request.
    """
    message: str
    image_path: Optional[str] = ""
    session_id: Optional[str] = ""
    part_file_name: str = "part_file_name"
    export_format: Optional[str] = None
    material_choice: str = "STEEL"
    selected_feature_uuid: Optional[str] = ""


class ChatResponse(BaseModel):
    """
    Schema for chat response.

    The chat_response field contains the chatbot's response message but does not include
    the generated code. It may contain:
    - Success messages when the model is created successfully
    - Error messages if something went wrong
    - Requests for additional information if parameters are missing
    - Other informational messages from the chatbot

    The actual generated code is not included in the response.
    """
    chat_response: str
    session_id: str
    obj_export: Optional[str] = None
    step_export: Optional[str] = None
    tessellated_export: Optional[Dict[str, Any]] = None
    attribute_and_transientid_map: Optional[Dict[str, Any]] = None
    manufacturing_errors: Optional[List[str]] = None


class SessionCreate(BaseModel):
    """
    Schema for creating a new session.
    If session_id is not provided, a new unique ID will be generated automatically.
    """
    session_id: Optional[str] = None
    name: Optional[str] = "User Session"


class SessionInfo(BaseModel):
    """
    Schema for detailed session information.
    """
    id: int
    session_id: str
    name: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SessionResponse(BaseModel):
    """
    Schema for session operation responses.
    """
    message: str


class ChatHistoryResponse(BaseModel):
    """
    Schema for chat history entries.
    """
    id: int
    message: str
    created_at: Optional[datetime] = None
    image_path: Optional[str] = None
    part_file_name: Optional[str] = None
    export_format: Optional[str] = None
    material_choice: Optional[str] = None

    class Config:
        from_attributes = True


class ExportResponse(BaseModel):
    """
    Schema for export file information.
    """
    format: str
    url: str


class TessellationResponse(BaseModel):
    """
    Schema for tessellated object data.
    """
    vertices: Optional[List[List[float]]] = None
    faces: Optional[List[List[int]]] = None
    normals: Optional[List[List[float]]] = None
